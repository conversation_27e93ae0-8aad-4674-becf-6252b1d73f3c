<template>
  <div id="app">
    <el-container>
      <el-header>
        <div class="header-content">
          <h1>MediaCrawler Web Interface</h1>
          <div class="header-actions">
            <el-button @click="refreshTasks" :loading="crawlerStore.isLoading">
              刷新任务列表
            </el-button>
          </div>
        </div>
      </el-header>
      
      <el-main>
        <el-row :gutter="20">
          <!-- 配置面板 -->
          <el-col :span="8">
            <el-card class="config-card">
              <template #header>
                <div class="card-header">
                  <span>爬虫配置</span>
                  <el-button size="small" @click="crawlerStore.resetConfig">
                    重置
                  </el-button>
                </div>
              </template>
              
              <ConfigForm />
            </el-card>
          </el-col>
          
          <!-- 任务列表和日志 -->
          <el-col :span="16">
            <el-card class="logs-card">
              <template #header>
                <div class="card-header">
                  <span>任务执行</span>
                  <div>
                    <el-button 
                      v-if="crawlerStore.currentTask" 
                      size="small" 
                      type="danger"
                      @click="stopCurrentTask"
                      :disabled="crawlerStore.currentTask.status !== 'running'"
                    >
                      停止任务
                    </el-button>
                    <el-button size="small" @click="clearLogs">
                      清空日志
                    </el-button>
                  </div>
                </div>
              </template>
              
              <TaskMonitor />
            </el-card>
          </el-col>
        </el-row>
        
        <!-- 任务历史 -->
        <el-card class="history-card" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>任务历史</span>
            </div>
          </template>
          
          <TaskHistory />
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useCrawlerStore } from '@/stores/crawler'
import ConfigForm from '@/components/ConfigForm.vue'
import TaskMonitor from '@/components/TaskMonitor.vue'
import TaskHistory from '@/components/TaskHistory.vue'

const crawlerStore = useCrawlerStore()

const refreshTasks = () => {
  crawlerStore.fetchAllTasks()
}

const stopCurrentTask = () => {
  if (crawlerStore.currentTask) {
    crawlerStore.stopTask(crawlerStore.currentTask.id)
  }
}

const clearLogs = () => {
  crawlerStore.clearLogs()
}

onMounted(() => {
  crawlerStore.fetchAllTasks()
})
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

body {
  margin: 0;
  padding: 0;
}

.el-header {
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-content h1 {
  margin: 0;
  font-size: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-card {
  height: fit-content;
}

.logs-card {
  min-height: 500px;
}

.history-card {
  max-height: 400px;
  overflow-y: auto;
}
</style>