version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MEDIA_CRAWLER_PATH=/app/examples/MediaCrawler
    volumes:
      - ./examples/MediaCrawler:/app/examples/MediaCrawler:ro
      - ./backend/logs:/app/logs
      - ./backend/temp:/app/temp
    depends_on:
      - frontend
    networks:
      - mediacrawler-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - mediacrawler-network

networks:
  mediacrawler-network:
    driver: bridge