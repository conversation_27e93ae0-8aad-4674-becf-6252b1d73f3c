<template>
  <el-form :model="config" label-width="120px" label-position="top">
    <!-- 平台选择 -->
    <el-form-item label="平台">
      <el-select v-model="config.platform" placeholder="请选择平台" style="width: 100%">
        <el-option label="小红书 (xhs)" value="xhs" />
        <el-option label="抖音 (douyin)" value="douyin" />
        <el-option label="哔哩哔哩 (bilibili)" value="bilibili" />
        <el-option label="微博 (weibo)" value="weibo" />
        <el-option label="百度贴吧 (tieba)" value="tieba" />
        <el-option label="快手 (kuaishou)" value="kuaishou" />
        <el-option label="知乎 (zhihu)" value="zhihu" />
      </el-select>
    </el-form-item>

    <!-- 关键词 -->
    <el-form-item label="关键词">
      <div class="keyword-input-group">
        <el-input
          v-model="keywordInput"
          placeholder="输入关键词后按回车添加"
          @keyup.enter="addKeyword"
          style="margin-bottom: 10px"
        />
        <el-button @click="addKeyword" size="small">添加</el-button>
      </div>
      <el-tag
        v-for="(keyword, index) in config.keywords"
        :key="index"
        closable
        @close="removeKeyword(index)"
        style="margin-right: 8px; margin-bottom: 4px"
      >
        {{ keyword }}
      </el-tag>
    </el-form-item>

    <!-- 爬虫类型 -->
    <el-form-item label="爬虫类型">
      <el-select v-model="config.crawler_type" style="width: 100%">
        <el-option label="搜索" value="search" />
        <el-option label="详情" value="detail" />
        <el-option label="创作者" value="creator" />
      </el-select>
    </el-form-item>

    <!-- 最大数量 -->
    <el-form-item label="最大爬取数量">
      <el-input-number v-model="config.max_count" :min="1" :max="10000" style="width: 100%" />
    </el-form-item>

    <!-- 起始页码 -->
    <el-form-item label="起始页码">
      <el-input-number v-model="config.start_page" :min="1" :max="100" style="width: 100%" />
    </el-form-item>

    <!-- 排序方式 -->
    <el-form-item label="排序方式" v-if="config.platform !== 'tieba'">
      <el-select v-model="config.sort_type" style="width: 100%">
        <el-option label="热门" value="popularity" />
        <el-option label="最新" value="latest" />
        <el-option label="默认" value="default" />
      </el-select>
    </el-form-item>

    <!-- Cookie -->
    <el-form-item label="Cookie (可选)">
      <el-input
        v-model="config.cookie"
        type="textarea"
        :rows="3"
        placeholder="输入登录后的Cookie信息"
        show-word-limit
      />
    </el-form-item>

    <!-- 数据保存格式 -->
    <el-form-item label="数据保存格式">
      <el-select v-model="config.save_data_option" style="width: 100%">
        <el-option label="JSON" value="json" />
        <el-option label="CSV" value="csv" />
        <el-option label="数据库" value="db" />
      </el-select>
    </el-form-item>

    <!-- 调试模式 -->
    <el-form-item>
      <el-checkbox v-model="config.enable_debug">启用调试模式</el-checkbox>
    </el-form-item>

    <!-- 日期范围 -->
    <el-form-item label="日期范围">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="width: 100%"
      />
    </el-form-item>

    <!-- 操作按钮 -->
    <el-form-item>
      <el-button
        type="primary"
        @click="startCrawler"
        :loading="crawlerStore.isLoading"
        :disabled="config.keywords.length === 0"
        style="width: 100%"
      >
        开始爬取
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useCrawlerStore } from '@/stores/crawler'

const crawlerStore = useCrawlerStore()
const config = computed(() => crawlerStore.config)
const keywordInput = ref('')

const dateRange = computed({
  get: () => {
    if (config.value.date_range) {
      return [new Date(config.value.date_range.start), new Date(config.value.date_range.end)]
    }
    return []
  },
  set: (value) => {
    if (value && value.length === 2) {
      config.value.date_range = {
        start: value[0].toISOString().split('T')[0],
        end: value[1].toISOString().split('T')[0]
      }
    } else {
      config.value.date_range = undefined
    }
  }
})

const addKeyword = () => {
  if (keywordInput.value.trim()) {
    crawlerStore.addKeyword(keywordInput.value.trim())
    keywordInput.value = ''
  }
}

const removeKeyword = (index: number) => {
  crawlerStore.removeKeyword(index)
}

const startCrawler = async () => {
  
  try {
    const taskId = await crawlerStore.startCrawler()
    if (taskId) {
      crawlerStore.connectToLogs(taskId)
    }
  } catch (error) {
    console.error('启动爬虫失败:', error)
  }
}
</script>

<style scoped>
.keyword-input-group {
  display: flex;
  gap: 8px;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}
</style>