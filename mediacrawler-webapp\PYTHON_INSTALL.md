# Python 安装指南 - MediaCrawler Web Interface

## 🚨 Python 未安装或未检测到

系统检测到Python未安装或未正确配置。请按以下步骤安装：

## Windows 安装

### 方法1: 官方安装器
1. 访问: https://www.python.org/downloads/windows/
2. 下载 **Python 3.8+** Windows安装器 (64-bit)
3. 运行安装器，**重要**：勾选 "**Add Python to PATH**"
4. 点击 "Install Now"
5. 安装完成后重启命令行窗口

### 方法2: Microsoft Store
1. 打开Microsoft Store
2. 搜索 "Python 3.11" 或最新版本
3. 点击安装

### 验证安装
```bash
python --version
# 或
python3 --version
# 或
py --version
```

## macOS 安装

### 方法1: Homebrew (推荐)
```bash
# 安装Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装Python
brew install python
```

### 方法2: 官方安装器
1. 访问: https://www.python.org/downloads/macos/
2. 下载macOS安装器
3. 运行安装

### 验证安装
```bash
python3 --version
```

## Linux 安装

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install python3 python3-pip
```

### CentOS/RHEL
```bash
sudo yum install python3 python3-pip
# 或对于新版本的RHEL/CentOS
sudo dnf install python3 python3-pip
```

### 验证安装
```bash
python3 --version
```

## MediaCrawler 依赖安装

安装Python后，还需要安装MediaCrawler的依赖：

```bash
# 进入MediaCrawler目录
cd ../examples/MediaCrawler

# 安装依赖
pip install -r requirements.txt

# 如果没有requirements.txt，手动安装常见依赖
pip install requests aiohttp selenium beautifulsoup4 lxml pandas
```

## 常见问题解决

### 1. pip 命令未找到
```bash
# Windows
python -m pip install --upgrade pip

# macOS/Linux
python3 -m pip install --upgrade pip
```

### 2. 权限问题
```bash
# macOS/Linux
python3 -m pip install --user -r requirements.txt
```

### 3. 多Python版本冲突
```bash
# 检查Python路径
where python    # Windows
which python3   # macOS/Linux

# 使用完整路径
# Windows: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
# macOS: /usr/local/bin/python3
# Linux: /usr/bin/python3
```

## 测试Python环境

安装完成后，运行测试：

```bash
node check-python.js
```

## 配置后端使用正确的Python命令

如果Python命令是 `python3` 而不是 `python`，可以在后端配置：

1. 创建 `backend/.env` 文件：
```
PYTHON_CMD=python3
```

2. 或修改后端代码中的pythonCmd变量

## 🎯 快速检查清单

- [ ] Python已安装
- [ ] 已添加到PATH
- [ ] pip命令可用
- [ ] MediaCrawler依赖已安装
- [ ] 运行 `node check-python.js` 通过

安装完成后，重新启动项目：
```bash
node init-project.js
```