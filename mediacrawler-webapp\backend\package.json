{"name": "mediacrawler-backend", "version": "1.0.0", "description": "Backend API for MediaCrawler web interface", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "test": "jest"}, "keywords": ["mediacrawler", "api", "crawler"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "winston": "^3.11.0", "cross-spawn": "^7.0.3", "chokidar": "^3.5.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/node": "^20.10.6", "@types/cross-spawn": "^6.0.6", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "jest": "^29.7.0", "@types/jest": "^29.5.11", "ts-jest": "^29.1.1"}}