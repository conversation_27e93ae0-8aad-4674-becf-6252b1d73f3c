# MediaCrawler Web Interface

基于 MediaCrawler 的 Web 界面应用，通过 Node.js + TypeScript 后端和 Vue3 + TypeScript 前端实现。

## 功能特性

- ✅ 支持多平台爬虫（小红书、抖音、哔哩哔哩、微博、百度贴吧、快手、知乎）
- ✅ 实时日志显示（通过 EventSource）
- ✅ 任务管理和历史记录
- ✅ 配置参数动态传入（不修改原始配置文件）
- ✅ 爬虫结果下载
- ✅ 任务状态监控
- ✅ 支持多人同时使用

## 技术栈

### 后端
- Node.js + TypeScript
- Express.js
- EventSource (SSE) 实时日志推送
- 子进程管理

### 前端
- Vue3 + TypeScript
- Element Plus UI 组件库
- Pinia 状态管理
- EventSource 实时日志接收

## 项目结构

```
mediacrawler-webapp/
├── backend/                 # Node.js 后端
│   ├── src/
│   │   ├── controllers/     # API 控制器
│   │   ├── services/        # 业务逻辑服务
│   │   ├── utils/           # 工具函数
│   │   └── index.ts         # 主入口文件
│   ├── package.json
│   └── tsconfig.json
├── frontend/                # Vue3 前端
│   ├── src/
│   │   ├── components/      # Vue 组件
│   │   ├── stores/          # Pinia 状态管理
│   │   ├── main.ts          # 主入口文件
│   │   └── App.vue          # 主应用组件
│   ├── package.json
│   └── vite.config.ts
├── shared/                  # 共享类型定义
│   └── types/
└── README.md
```

## 快速开始

### 1. 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 2. 配置环境

确保 MediaCrawler 项目已正确安装在 `examples/MediaCrawler` 目录下。

### 3. 启动服务

```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务（新终端）
cd frontend
npm run dev
```

### 4. 访问应用

- 前端界面: http://localhost:3000
- 后端 API: http://localhost:3001

## API 接口

### 启动爬虫
```
POST /api/crawler/start
Body: CrawlerConfig
Response: { success: true, task_id: string }
```

### 获取任务状态
```
GET /api/crawler/tasks/:taskId
Response: { success: true, data: CrawlerTask }
```

### 获取所有任务
```
GET /api/crawler/tasks
Response: { success: true, data: CrawlerTask[] }
```

### 停止任务
```
POST /api/crawler/tasks/:taskId/stop
Response: { success: true, message: string }
```

### 下载结果
```
GET /api/crawler/tasks/:taskId/download
Response: 文件下载
```

### 实时日志
```
GET /api/crawler/tasks/:taskId/logs
Response: EventSource (SSE)
```

## 配置参数

### CrawlerConfig

```typescript
interface CrawlerConfig {
  platform: 'xhs' | 'douyin' | 'bilibili' | 'weibo' | 'tieba' | 'kuaishou' | 'zhihu'
  keywords: string[]
  max_count: number
  cookie: string
  enable_debug: boolean
  save_data_option: 'csv' | 'json' | 'db'
  crawler_type: 'search' | 'detail' | 'creator'
  start_page: number
  sort_type?: 'popularity' | 'latest' | 'default'
  date_range?: {
    start: string
    end: string
  }
}
```

## 使用说明

### 1. 配置爬虫参数
- 选择目标平台
- 输入关键词（支持多个）
- 设置爬取数量和起始页码
- 可选：输入Cookie、设置日期范围等

### 2. 启动爬虫
- 点击"开始爬取"按钮
- 实时查看爬虫进度和日志

### 3. 下载结果
- 任务完成后点击"下载结果"按钮
- 结果以JSON格式下载到本地

### 4. 任务管理
- 查看任务历史记录
- 停止正在运行的任务
- 重新下载历史任务的结果

## 开发说明

### 后端开发
```bash
cd backend
npm run dev    # 开发模式
npm run build  # 构建
npm run start  # 生产模式
```

### 前端开发
```bash
cd frontend
npm run dev     # 开发模式
npm run build   # 构建
npm run preview # 预览构建结果
```

## 注意事项

1. **Python环境**: 确保系统已安装 Python 3.8+ 和 MediaCrawler 所需的依赖
2. **Cookie配置**: 对于需要登录的平台，建议提供有效的 Cookie
3. **并发限制**: 系统支持多人同时使用，但建议控制并发数量
4. **结果文件**: 结果文件保存在 MediaCrawler 的 data 目录下
5. **日志清理**: 任务完成后24小时的日志会被自动清理

## 故障排除

### 常见问题

1. **Python未找到**: 确保 Python 已添加到系统 PATH
2. **MediaCrawler路径错误**: 检查 `examples/MediaCrawler` 目录是否存在
3. **端口占用**: 修改后端或前端的端口配置
4. **跨域问题**: 已配置 CORS，确保前后端端口正确

### 调试模式

启动后端时设置环境变量：
```bash
NODE_ENV=development npm run dev
```

查看详细日志：`backend/logs/combined.log`