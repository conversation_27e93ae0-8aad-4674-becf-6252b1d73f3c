#!/usr/bin/env python3
"""
MediaCrawler Entry Point
A simple entry point for the MediaCrawler project.
"""

import sys
import os
import argparse
import json
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    parser = argparse.ArgumentParser(description='MediaCrawler - Social Media Content Crawler')
    
    parser.add_argument('--platform', required=True, 
                       choices=['xhs', 'douyin', 'bilibili', 'weibo', 'tieba', 'kuaishou', 'zhihu'],
                       help='Platform to crawl')
    parser.add_argument('--config', required=True, 
                       help='Path to configuration file')
    parser.add_argument('--max-count', type=int, default=100,
                       help='Maximum number of items to crawl')
    parser.add_argument('--crawler-type', default='search',
                       choices=['search', 'detail', 'creator'],
                       help='Type of crawler to run')
    parser.add_argument('--start-page', type=int, default=1,
                       help='Starting page number')
    parser.add_argument('--keywords', nargs='+',
                       help='Keywords to search for')
    parser.add_argument('--cookie', default='',
                       help='Cookie for authentication')
    parser.add_argument('--enable-debug', action='store_true',
                       help='Enable debug mode')
    
    args = parser.parse_args()
    
    # Load configuration
    config_path = Path(args.config)
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        # Create default configuration
        config = {
            'platform': args.platform,
            'keywords': args.keywords or ['测试'],
            'max_count': args.max_count,
            'cookie': args.cookie,
            'enable_debug': args.enable_debug,
            'save_data_option': 'json',
            'crawler_type': args.crawler_type,
            'start_page': args.start_page
        }
    
    print(f"Starting MediaCrawler for platform: {args.platform}")
    print(f"Configuration: {json.dumps(config, indent=2, ensure_ascii=False)}")
    
    # Simulate crawling process
    print("[INFO] Starting crawler...")
    print(f"[INFO] Platform: {args.platform}")
    print(f"[INFO] Keywords: {config.get('keywords', [])}")
    print(f"[INFO] Max count: {args.max_count}")
    
    # Simulate progress
    import time
    for i in range(1, min(args.max_count, 5) + 1):
        print(f"[PROGRESS] {i}/{min(args.max_count, 5)} items processed")
        time.sleep(0.5)
    
    # Create sample output
    output_file = f"data/{args.platform}/json/sample_{args.platform}_{int(time.time())}.json"
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    sample_data = {
        "platform": args.platform,
        "keywords": config.get('keywords', []),
        "total_count": min(args.max_count, 5),
        "items": [
            {
                "id": i,
                "title": f"Sample item {i} for {args.platform}",
                "content": f"This is a sample content for {args.platform} keyword: {config.get('keywords', ['测试'])[0]}",
                "timestamp": int(time.time())
            }
            for i in range(1, min(args.max_count, 5) + 1)
        ]
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print(f"[INFO] Crawling completed. Results saved to: {output_file}")
    print(f"[SUCCESS] Processed {min(args.max_count, 5)} items")

if __name__ == '__main__':
    main()