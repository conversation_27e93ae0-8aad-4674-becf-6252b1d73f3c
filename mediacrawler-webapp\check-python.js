#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 检查Python环境...\n');

function detectPython() {
    const pythonCommands = ['python', 'python3', 'py'];
    
    for (const cmd of pythonCommands) {
        try {
            const version = execSync(`${cmd} --version`, { encoding: 'utf8' }).trim();
            console.log(`✅ 找到 ${version} - 命令: ${cmd}`);
            return cmd;
        } catch (error) {
            continue;
        }
    }
    
    return null;
}

function checkPython() {
    console.log('🔍 正在检测Python...');
    
    const pythonCmd = detectPython();
    
    if (!pythonCmd) {
        console.log('❌ Python 未检测到');
        console.log('\n📋 请安装Python:');
        console.log('1. 访问: https://www.python.org/downloads/');
        console.log('2. 下载Python 3.8或更高版本');
        console.log('3. 安装时勾选 "Add Python to PATH"');
        console.log('4. 重新运行此脚本');
        return false;
    }
    
    console.log(`\n✅ Python 命令: ${pythonCmd}`);
    return pythonCmd;
}

function checkMediaCrawlerPythonEnv(pythonCmd) {
    console.log('\n🐍 检查MediaCrawler Python环境...');
    
    const mediaCrawlerPath = path.join(__dirname, '..', 'examples', 'MediaCrawler');
    
    try {
        // 检查requirements.txt是否存在
        const requirementsPath = path.join(mediaCrawlerPath, 'requirements.txt');
        if (fs.existsSync(requirementsPath)) {
            console.log('📦 发现requirements.txt，检查依赖...');
            
            // 尝试检查已安装的包
            try {
                execSync(`${pythonCmd} -c "import requests, aiohttp, selenium"`, {
                    cwd: mediaCrawlerPath,
                    stdio: 'pipe'
                });
                console.log('✅ MediaCrawler依赖已安装');
                return true;
            } catch (error) {
                console.log('⚠️  部分依赖缺失，需要安装');
                console.log('\n📋 安装MediaCrawler依赖:');
                console.log(`cd ${mediaCrawlerPath}`);
                console.log(`${pythonCmd} -m pip install -r requirements.txt`);
                return false;
            }
        } else {
            console.log('⚠️  未找到requirements.txt');
            return true; // 假设MediaCrawler已配置好
        }
    } catch (error) {
        console.log('⚠️  无法检查MediaCrawler环境');
        return true;
    }
}

function main() {
    const pythonCmd = checkPython();
    
    if (!pythonCmd) {
        console.log('\n🚨 Python安装指南:');
        console.log('==================');
        console.log('Windows:');
        console.log('1. 打开: https://www.python.org/downloads/windows/');
        console.log('2. 下载Python 3.8+ Windows安装器');
        console.log('3. 运行安装器，勾选 "Add Python to PATH"');
        console.log('4. 安装完成后重启命令行');
        console.log('');
        console.log('macOS:');
        console.log('1. 打开终端');
        console.log('2. 运行: brew install python3');
        console.log('');
        console.log('Linux:');
        console.log('1. Ubuntu/Debian: sudo apt install python3 python3-pip');
        console.log('2. CentOS/RHEL: sudo yum install python3 python3-pip');
        process.exit(1);
    }
    
    const envReady = checkMediaCrawlerPythonEnv(pythonCmd);
    
    if (envReady) {
        console.log('\n🎉 Python环境检查通过！');
        console.log(`可以在后端配置中使用: ${pythonCmd}`);
    } else {
        console.log('\n⚠️  请安装依赖后重新运行');
    }
}

if (require.main === module) {
    main();
}

module.exports = { detectPython, checkPython };
