import { spawn, ChildProcess, execSync } from 'child_process';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import { CrawlerConfig, CrawlerTask, LogEntry } from '../../../shared/types/mediacrawler';
export class CrawlerService extends EventEmitter {
  private tasks: Map<string, CrawlerTask> = new Map();
  private processes: Map<string, ChildProcess> = new Map();
  private mediaCrawlerPath: string;
  private pythonCmd: string;

  constructor() {
    super();
    // 设置MediaCrawler路径
    this.mediaCrawlerPath = path.resolve(
        __dirname,
        '../../../../examples/MediaCrawler'
    );
    
    // 自动检测Python命令
    this.pythonCmd = this.detectPythonCommand();
    console.log(`使用Python命令: ${this.pythonCmd}`);
  }

  async startCrawler(config: CrawlerConfig): Promise<string> {
    const taskId = uuidv4();
    
    const task: CrawlerTask = {
      id: taskId,
      config,
      status: 'pending',
      progress: 0,
      logs: [],
      created_at: new Date()
    };

    this.tasks.set(taskId, task);
    console.log(this.tasks.size,'进入了');
    
    try {
      await this.executeCrawler(taskId, config);
      return taskId;
    } catch (error) {
      task.status = 'failed';
      task.error = error instanceof Error ? error.message : String(error);
      throw error;
    }
  }

  private async executeCrawler(taskId: string, config: CrawlerConfig): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) throw new Error('Task not found');

    task.status = 'running';
    task.started_at = new Date();

    // 创建临时配置文件
    const tempConfigPath = path.join(__dirname, '../../temp', `config_${taskId}.json`);
    
    // 确保temp目录存在
    const tempDir = path.dirname(tempConfigPath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // 写入临时配置文件
    fs.writeFileSync(tempConfigPath, JSON.stringify(config, null, 2));

    // 构建命令参数
    let cmd: string;
    let args: string[];
    if (this.pythonCmd === 'uv') {
      // 使用 uv run 命令
      cmd = 'uv';
      args = [
        'run',
        'main.py',
        '--platform', config.platform,
        '--config', tempConfigPath,
        '--max-count', config.max_count.toString(),
        '--crawler-type', config.crawler_type,
        '--start-page', config.start_page.toString()
      ];
    } else {
      // 使用 Python 命令
      cmd = this.pythonCmd;
      args = [
        path.join(this.mediaCrawlerPath, 'main.py'),
        '--platform', config.platform,
        '--config', tempConfigPath,
        '--max-count', config.max_count.toString(),
        '--crawler-type', config.crawler_type,
        '--start-page', config.start_page.toString()
      ];
    }

    if (config.keywords.length > 0) {
      args.push('--keywords', ...config.keywords);
    }

    if (config.cookie) {
      args.push('--cookie', config.cookie);
    }

    if (config.enable_debug) {
      args.push('--enable-debug');
    }
    
    // 启动子进程
    console.log(`执行命令: ${cmd} ${args.join(' ')}`);
    const process = spawn(cmd, args, {
      cwd: this.mediaCrawlerPath,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    this.processes.set(taskId, process);
    // 处理标准输出
    process.stdout?.on('data', (data) => {
      console.log('process.stdout成功了');
      
      const message = data.toString().trim();
      this.addLog(taskId, 'info', message);
      this.parseProgress(taskId, message);
    });

    // 处理标准错误
    process.stderr?.on('data', (data) => {
      console.log('stderr: ', data.toString());
      
      const message = data.toString().trim();
      this.addLog(taskId, 'error', message);
    });

    // 处理进程退出
    process.on('close', (code) => {
      console.log('process.on(close)', code);
      
      this.handleProcessExit(taskId, code || 0);
      
      // 清理临时配置文件
      if (fs.existsSync(tempConfigPath)) {
        fs.unlinkSync(tempConfigPath);
      }
    });

    process.on('error', (error) => {
      console.log('process.on(error)', error);
      
      task.status = 'failed';
      task.error = error.message;
      this.addLog(taskId, 'error', `进程错误: ${error.message}`);
    });
  }

  private parseProgress(taskId: string, message: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    // 尝试从日志消息中提取进度信息
    const progressMatch = message.match(/(\d+)\/(\d+)/) || message.match(/(\d+)%/);
    if (progressMatch) {
      const progress = parseInt(progressMatch[1]);
      const total = progressMatch[2] ? parseInt(progressMatch[2]) : 100;
      task.progress = Math.round((progress / total) * 100);
    }

    // 检查是否完成
    if (message.includes('完成') || message.includes('finished') || message.includes('done')) {
      task.progress = 100;
    }
  }

  private handleProcessExit(taskId: string, code: number): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.completed_at = new Date();

    if (code === 0) {
      task.status = 'completed';
      
      // 查找结果文件
      const resultFile = this.findResultFile(taskId, task.config);
      if (resultFile) {
        task.result_file = resultFile;
      }
      
      this.addLog(taskId, 'info', '爬虫任务完成');
    } else {
      task.status = 'failed';
      task.error = `进程退出码: ${code}`;
      this.addLog(taskId, 'error', `爬虫任务失败，退出码: ${code}`);
    }

    this.processes.delete(taskId);
    this.emit('taskComplete', taskId);
  }

  private detectPythonCommand(): string {
    // 优先使用环境变量中的配置
    if (process.env.PYTHON_CMD) {
      return process.env.PYTHON_CMD;
    }

    // 检查是否使用uv运行
    try {
      execSync('uv --version', { stdio: 'pipe' });
      return 'uv';
    } catch (error) {
      // 如果uv不可用，使用Python命令
    }

    // Windows环境下优先使用py命令
    if (process.platform === 'win32') {
      try {
        execSync('py --version', { stdio: 'pipe' });
        return 'py';
      } catch (error) {
        // 如果py命令不可用，继续尝试其他命令
      }
    }

    const pythonCommands = ['python3', 'python', 'py'];
    
    for (const cmd of pythonCommands) {
      try {
        execSync(`${cmd} --version`, { stdio: 'pipe' });
        return cmd;
      } catch (error) {
        continue;
      }
    }
    
    throw new Error('Python 未安装或未添加到PATH环境变量');
  }

  private findResultFile(taskId: string, config: CrawlerConfig): string | undefined {
    const dataDir = path.join(this.mediaCrawlerPath, 'data', config.platform);
    if (!fs.existsSync(dataDir)) return undefined;

    const files = fs.readdirSync(dataDir);
    const jsonFiles = files.filter(f => f.endsWith('.json'));
    
    // 找到最新的JSON文件
    const latestFile = jsonFiles
      .map(f => ({ name: f, time: fs.statSync(path.join(dataDir, f)).mtime }))
      .sort((a, b) => b.time.getTime() - a.time.getTime())[0];

    return latestFile ? path.join(dataDir, latestFile.name) : undefined;
  }

  private addLog(taskId: string, level: 'info' | 'warn' | 'error' | 'debug', message: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      task_id: taskId
    };

    task.logs.push(`[${logEntry.timestamp}] [${level.toUpperCase()}] ${message}`);
    
    // 限制日志数量，避免内存溢出
    if (task.logs.length > 1000) {
      task.logs = task.logs.slice(-500);
    }

    this.emit('log', logEntry);
  }

  getTask(taskId: string): CrawlerTask | undefined {
    return this.tasks.get(taskId);
  }

  getAllTasks(): CrawlerTask[] {
    return Array.from(this.tasks.values());
  }

  stopTask(taskId: string): boolean {
    const process = this.processes.get(taskId);
    if (process) {
      process.kill('SIGTERM');
      this.processes.delete(taskId);
      
      const task = this.tasks.get(taskId);
      if (task) {
        task.status = 'failed';
        task.error = '任务被用户停止';
      }
      return true;
    }
    return false;
  }

  async downloadResult(taskId: string): Promise<string | undefined> {
    const task = this.tasks.get(taskId);
    return task?.result_file;
  }

  cleanupOldTasks(): void {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    for (const [taskId, task] of this.tasks.entries()) {
      if (task.completed_at && task.completed_at < oneDayAgo) {
        this.tasks.delete(taskId);
      }
    }
  }
}