<template>
  <div class="task-monitor">
    <!-- 任务状态 -->
    <div v-if="crawlerStore.currentTask" class="task-status">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="任务ID">
          {{ crawlerStore.currentTask.id }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="statusType(crawlerStore.currentTask.status)">
            {{ statusText(crawlerStore.currentTask.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="平台">
          {{ crawlerStore.currentTask.config.platform }}
        </el-descriptions-item>
        <el-descriptions-item label="关键词">
          {{ crawlerStore.currentTask.config.keywords.join(', ') }}
        </el-descriptions-item>
        <el-descriptions-item label="进度">
          <el-progress :percentage="crawlerStore.currentTask.progress" />
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">
          {{ formatTime(crawlerStore.currentTask.started_at) }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 操作按钮 -->
    <div v-if="crawlerStore.currentTask" class="task-actions" style="margin-top: 16px">
      <el-button
        v-if="crawlerStore.currentTask.status === 'completed'"
        type="success"
        @click="downloadResult"
        :disabled="!crawlerStore.currentTask.result_file"
      >
        下载结果
      </el-button>
    </div>

    <!-- 实时日志 -->
    <div class="logs-container">
      <div class="logs-header">
        <h4>实时日志</h4>
        <el-button size="small" @click="toggleAutoScroll">
          {{ autoScroll ? '停止自动滚动' : '启用自动滚动' }}
        </el-button>
      </div>
      
      <div ref="logContainer" class="log-content">
        <div
          v-for="(log, index) in crawlerStore.logs"
          :key="index"
          class="log-line"
          :class="getLogClass(log)"
        >
          {{ log }}
        </div>
        <div v-if="crawlerStore.logs.length === 0" class="no-logs">
          暂无日志，开始爬虫任务后将显示实时日志...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted } from 'vue'
import { useCrawlerStore } from '@/stores/crawler'
import dayjs from 'dayjs'

const crawlerStore = useCrawlerStore()
const logContainer = ref<HTMLElement>()
const autoScroll = ref(true)

const statusType = (status: string) => {
  const map: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return map[status] || 'info'
}

const statusText = (status: string) => {
  const map: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '已失败'
  }
  return map[status] || status
}

const formatTime = (time?: Date) => {
  if (!time) return '-'
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const getLogClass = (log: string) => {
  if (log.includes('[ERROR]')) return 'log-error'
  if (log.includes('[WARN]')) return 'log-warn'
  if (log.includes('[INFO]')) return 'log-info'
  return 'log-default'
}

const scrollToBottom = () => {
  if (autoScroll.value && logContainer.value) {
    nextTick(() => {
      logContainer.value!.scrollTop = logContainer.value!.scrollHeight
    })
  }
}

const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value
  if (autoScroll.value) {
    scrollToBottom()
  }
}

const downloadResult = async () => {
  if (crawlerStore.currentTask?.id) {
    await crawlerStore.downloadResult(crawlerStore.currentTask.id)
  }
}

// 监听日志变化，自动滚动
watch(() => crawlerStore.logs.length, () => {
  scrollToBottom()
})

onMounted(() => {
  // 初始滚动到底部
  scrollToBottom()
})
</script>

<style scoped>
.task-monitor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.task-status {
  margin-bottom: 16px;
}

.task-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.logs-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.logs-header h4 {
  margin: 0;
  color: #303133;
}

.log-content {
  flex: 1;
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.4;
  overflow-y: auto;
  max-height: 400px;
}

.log-line {
  margin-bottom: 2px;
  word-break: break-all;
}

.log-error {
  color: #f48771;
}

.log-warn {
  color: #dcdcaa;
}

.log-info {
  color: #569cd6;
}

.log-default {
  color: #d4d4d4;
}

.no-logs {
  color: #808080;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}
</style>