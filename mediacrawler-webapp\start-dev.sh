#!/bin/bash

echo "🚀 启动 MediaCrawler Web Interface 开发环境..."

# 检查是否安装了必要的命令
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 检查 MediaCrawler 目录是否存在
if [ ! -d "../examples/MediaCrawler" ]; then
    echo "❌ MediaCrawler 目录不存在，请确保 examples/MediaCrawler 已下载"
    exit 1
fi

echo "📦 安装后端依赖..."
cd backend
if [ ! -d "node_modules" ]; then
    npm install
fi

echo "📦 安装前端依赖..."
cd ../frontend
if [ ! -d "node_modules" ]; then
    npm install
fi

echo "🎯 启动开发服务器..."
cd ..

# 启动后端
start_backend() {
    echo "🌐 启动后端服务 (端口: 3001)..."
    cd backend
    npm run dev
}

# 启动前端
start_frontend() {
    sleep 3  # 等待后端启动
    echo "🎨 启动前端服务 (端口: 3000)..."
    cd frontend
    npm run dev
}

# 并行启动两个服务
start_backend &
start_frontend &

echo "✅ 启动完成！"
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:3001"
echo "按 Ctrl+C 停止所有服务"

# 等待所有后台进程
wait