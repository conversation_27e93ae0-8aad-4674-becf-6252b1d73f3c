import { CrawlerService } from '../CrawlerService';
import { CrawlerConfig } from '../../../../../shared/types/mediacrawler';

describe('CrawlerService', () => {
  let service: CrawlerService;

  beforeEach(() => {
    service = new CrawlerService();
  });

  afterEach(() => {
    service.cleanupOldTasks();
  });

  describe('validateConfig', () => {
    it('should validate correct config', () => {
      const validConfig: CrawlerConfig = {
        platform: 'xhs',
        keywords: ['test'],
        max_count: 100,
        cookie: 'test_cookie',
        enable_debug: false,
        save_data_option: 'json',
        crawler_type: 'search',
        start_page: 1,
      };

      expect(() => service.startCrawler(validConfig)).not.toThrow();
    });

    it('should reject invalid platform', () => {
      const invalidConfig = {
        platform: 'invalid',
        keywords: ['test'],
        max_count: 100,
        cookie: 'test',
        enable_debug: false,
        save_data_option: 'json',
        crawler_type: 'search',
        start_page: 1,
      } as CrawlerConfig;

      expect(() => service.startCrawler(invalidConfig)).rejects.toThrow();
    });
  });

  describe('task management', () => {
    it('should create task with unique id', async () => {
      const config: CrawlerConfig = {
        platform: 'xhs',
        keywords: ['test1'],
        max_count: 10,
        cookie: '',
        enable_debug: false,
        save_data_option: 'json',
        crawler_type: 'search',
        start_page: 1,
      };

      const taskId = await service.startCrawler(config);
      const task = service.getTask(taskId);

      expect(task).toBeDefined();
      expect(task?.id).toBe(taskId);
      expect(task?.status).toBe('pending');
    });

    it('should cleanup old tasks', () => {
      const oldTask = {
        id: 'old-task',
        config: {} as CrawlerConfig,
        status: 'completed' as const,
        progress: 100,
        logs: [],
        created_at: new Date(),
        completed_at: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 hours ago
      };

      service['tasks'].set('old-task', oldTask);
      service.cleanupOldTasks();

      expect(service.getTask('old-task')).toBeUndefined();
    });
  });
});