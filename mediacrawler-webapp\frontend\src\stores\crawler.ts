import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import axios from 'axios'
import type { CrawlerConfig, CrawlerTask, CrawlerResponse } from '../../../shared/types/mediacrawler'

const API_BASE_URL = '/api'

export const useCrawlerStore = defineStore('crawler', () => {
  const tasks = ref<CrawlerTask[]>([])
  const currentTask = ref<CrawlerTask | null>(null)
  const isLoading = ref(false)
  const logs = ref<string[]>([])
  const eventSource = ref<EventSource | null>(null)

  const config = reactive<CrawlerConfig>({
    platform: 'xhs',
    keywords: [],
    max_count: 100,
    cookie: '',
    enable_debug: false,
    save_data_option: 'json',
    crawler_type: 'search',
    start_page: 1,
    sort_type: 'popularity',
    date_range: undefined
  })

  const startCrawler = async () => {
    isLoading.value = true
    try {
      const response = await axios.post<CrawlerResponse>(`${API_BASE_URL}/crawler/start`, config)
      if (response.data.success && response.data.task_id) {
        await fetchTaskStatus(response.data.task_id)
        return response.data.task_id
      } else {
        throw new Error(response.data.error || '启动爬虫失败')
      }
    } catch (error) {
      console.error('启动爬虫失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const fetchTaskStatus = async (taskId: string) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/crawler/tasks/${taskId}`)
      console.log('response', response);
      
      if (response.data.success) {
        currentTask.value = response.data.data
        updateTasksList(response.data.data)
      }
    } catch (error) {
      console.error('获取任务状态失败:', error)
    }
  }
  const fetchAllTasks = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/crawler/tasks`)
      if (response.data.success) {
        tasks.value = response.data.data
      }
    } catch (error) {
      console.error('获取任务列表失败:', error)
    }
  }

  const stopTask = async (taskId: string) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/crawler/tasks/${taskId}/stop`)
      if (response.data.success) {
        await fetchTaskStatus(taskId)
      }
    } catch (error) {
      console.error('停止任务失败:', error)
    }
  }

  const downloadResult = async (taskId: string) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/crawler/tasks/${taskId}/download`, {
        responseType: 'blob'
      })
      
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `crawler_result_${taskId}.json`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载结果失败:', error)
      throw error
    }
  }

  const connectToLogs = (taskId: string) => {
    if (eventSource.value) {
      eventSource.value.close()
    }

    eventSource.value = new EventSource(`${API_BASE_URL}/crawler/tasks/${taskId}/logs`)
    
    eventSource.value.onmessage = (event) => {
      const data = JSON.parse(event.data)
      if (data.type === 'log') {
        logs.value.push(`[${data.timestamp}] [${data.level}] ${data.message}`)
      } else if (data.type === 'taskUpdate') {
        currentTask.value = data.task
        updateTasksList(data.task)
      } else if (data.type === 'heartbeat') {
        // 心跳消息，保持连接
      }
    }

    eventSource.value.onerror = (error) => {
      console.error('日志连接错误:', error)
      eventSource.value?.close()
    }
  }

  const disconnectFromLogs = () => {
    if (eventSource.value) {
      eventSource.value.close()
      eventSource.value = null
    }
  }

  const clearLogs = () => {
    logs.value = []
  }

  const updateTasksList = (updatedTask: CrawlerTask) => {
    const index = tasks.value.findIndex(t => t.id === updatedTask.id)
    if (index !== -1) {
      tasks.value[index] = updatedTask
    } else {
      tasks.value.unshift(updatedTask)
    }
  }

  const addKeyword = (keyword: string) => {
    if (keyword.trim() && !config.keywords.includes(keyword.trim())) {
      config.keywords.push(keyword.trim())
    }
  }

  const removeKeyword = (index: number) => {
    config.keywords.splice(index, 1)
  }

  const resetConfig = () => {
    Object.assign(config, {
      platform: 'xhs',
      keywords: [],
      max_count: 100,
      cookie: '',
      enable_debug: false,
      save_data_option: 'json',
      crawler_type: 'search',
      start_page: 1,
      sort_type: 'popularity',
      date_range: undefined
    })
  }

  return {
    tasks,
    currentTask,
    isLoading,
    logs,
    config,
    startCrawler,
    fetchTaskStatus,
    fetchAllTasks,
    stopTask,
    downloadResult,
    connectToLogs,
    disconnectFromLogs,
    clearLogs,
    addKeyword,
    removeKeyword,
    resetConfig
  }
})