export interface CrawlerConfig {
  platform: 'xhs' | 'douyin' | 'bilibili' | 'weibo' | 'tieba' | 'kuaishou' | 'zhihu';
  keywords: string[];
  max_count: number;
  cookie: string;
  enable_debug: boolean;
  save_data_option: 'csv' | 'json' | 'db';
  crawler_type: 'search' | 'detail' | 'creator';
  start_page: number;
  sort_type?: 'popularity' | 'latest' | 'default';
  filter_type?: string;
  date_range?: {
    start: string;
    end: string;
  };
}

export interface CrawlerTask {
  id: string;
  config: CrawlerConfig;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  logs: string[];
  result_file?: string;
  created_at: Date;
  started_at?: Date;
  completed_at?: Date;
  error?: string;
}

export interface CrawlerResponse {
  success: boolean;
  data?: any;
  error?: string;
  task_id?: string;
}

export interface LogEntry {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  task_id: string;
}