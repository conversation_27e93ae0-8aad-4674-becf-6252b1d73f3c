import { Request, Response } from 'express';
import { CrawlerService } from '../services/CrawlerService';
import { CrawlerConfig, CrawlerResponse } from '../../../shared/types/mediacrawler';

export class CrawlerController {
  private crawlerService: CrawlerService;

  constructor() {
    this.crawlerService = new CrawlerService();
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.crawlerService.on('log', (logEntry) => {
      // 日志事件处理将在SSE中处理
    });

    this.crawlerService.on('taskComplete', (taskId) => {
      // 任务完成事件处理
    });
  }

  async startCrawler(req: Request, res: Response): Promise<void> {
    
    try {
      const config: CrawlerConfig = req.body;
      
      // 验证配置
      if (!this.validateConfig(config)) {
        const response: CrawlerResponse = {
          success: false,
          error: '配置参数无效'
        };
        res.status(400).json(response);
        return;
      }

      const taskId = await this.crawlerService.startCrawler(config);
      
      const response: CrawlerResponse = {
        success: true,
        task_id: taskId
      };

      res.json(response);
    } catch (error) {
      const response: CrawlerResponse = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
      res.status(500).json(response);
    }
  }

  async getTaskStatus(req: Request, res: Response): Promise<void> {
    console.log('进入了');
    try {
      const { taskId } = req.params;
      console.log('taskId', taskId);
      
      const task = this.crawlerService.getTask(taskId);
      console.log('task', task);
      
      if (!task) {
        const response: CrawlerResponse = {
          success: false,
          error: '任务不存在'
        };
        res.status(404).json(response);
        return;
      }

      res.json({
        success: true,
        data: '12345'
      });
    } catch (error) {
      console.log('进入了error未知错误');
      
      const response: CrawlerResponse = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
      res.status(500).json(response);
    }
  }

  async getAllTasks(req: Request, res: Response): Promise<void> {
    try {
      const tasks = this.crawlerService.getAllTasks();
      res.json({
        success: true,
        data: tasks
      });
    } catch (error) {
      const response: CrawlerResponse = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
      res.status(500).json(response);
    }
  }

  async stopTask(req: Request, res: Response): Promise<void> {
    try {
      const { taskId } = req.params;
      const stopped = this.crawlerService.stopTask(taskId);

      if (!stopped) {
        const response: CrawlerResponse = {
          success: false,
          error: '任务不存在或已停止'
        };
        res.status(404).json(response);
        return;
      }

      res.json({
        success: true,
        message: '任务已停止'
      });
    } catch (error) {
      const response: CrawlerResponse = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
      res.status(500).json(response);
    }
  }

  async downloadResult(req: Request, res: Response): Promise<void> {
    try {
      const { taskId } = req.params;
      const resultFile = await this.crawlerService.downloadResult(taskId);

      if (!resultFile || !require('fs').existsSync(resultFile)) {
        const response: CrawlerResponse = {
          success: false,
          error: '结果文件不存在'
        };
        res.status(404).json(response);
        return;
      }

      const filename = require('path').basename(resultFile);
      res.download(resultFile, filename);
    } catch (error) {
      const response: CrawlerResponse = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
      res.status(500).json(response);
    }
  }

  async streamLogs(req: Request, res: Response): Promise<void> {
    const { taskId } = req.params;
    
    // 设置SSE响应头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
    });

    res.write(`data: ${JSON.stringify({ type: 'connected', message: '日志连接已建立' })}\n\n`);

    const sendLog = (logEntry: any) => {
      if (logEntry.task_id === taskId) {
        res.write(`data: ${JSON.stringify(logEntry)}\n\n`);
      }
    };

    const sendTaskUpdate = (updatedTaskId: string) => {
      if (updatedTaskId === taskId) {
        const task = this.crawlerService.getTask(taskId);
        if (task) {
          res.write(`data: ${JSON.stringify({ type: 'taskUpdate', task })}\n\n`);
        }
      }
    };

    this.crawlerService.on('log', sendLog);
    this.crawlerService.on('taskComplete', sendTaskUpdate);

    // 发送当前任务状态
    const task = this.crawlerService.getTask(taskId);
    if (task) {
      res.write(`data: ${JSON.stringify({ type: 'taskUpdate', task })}\n\n`);
      
      // 发送历史日志
      task.logs.forEach(log => {
        res.write(`data: ${JSON.stringify({ message: log, type: 'history' })}\n\n`);
      });
    }

    // 心跳
    const heartbeat = setInterval(() => {
      res.write(`data: ${JSON.stringify({ type: 'heartbeat' })}\n\n`);
    }, 30000);

    // 清理连接
    req.on('close', () => {
      clearInterval(heartbeat);
      this.crawlerService.off('log', sendLog);
      this.crawlerService.off('taskComplete', sendTaskUpdate);
    });
  }

  private validateConfig(config: any): config is CrawlerConfig {
    if (!config || typeof config !== 'object') return false;
    
    const validPlatforms = ['xhs', 'douyin', 'bilibili', 'weibo', 'tieba', 'kuaishou', 'zhihu'];
    if (!validPlatforms.includes(config.platform)) return false;
    
    if (!Array.isArray(config.keywords) || config.keywords.length === 0) return false;
    if (typeof config.max_count !== 'number' || config.max_count <= 0) return false;
    if (typeof config.cookie !== 'string') return false;
    if (typeof config.enable_debug !== 'boolean') return false;
    
    const validSaveOptions = ['csv', 'json', 'db'];
    if (!validSaveOptions.includes(config.save_data_option)) return false;
    
    const validCrawlerTypes = ['search', 'detail', 'creator'];
    if (!validCrawlerTypes.includes(config.crawler_type)) return false;
    
    if (typeof config.start_page !== 'number' || config.start_page < 1) return false;
    
    return true;
  }
}