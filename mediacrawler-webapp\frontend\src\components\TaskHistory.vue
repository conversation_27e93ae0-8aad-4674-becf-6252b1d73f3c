<template>
  <div class="task-history">
    <el-table :data="tasks" style="width: 100%" height="300">
      <el-table-column prop="id" label="任务ID" width="120">
        <template #default="scope">
          <span class="task-id" :title="scope.row.id">{{ scope.row.id.substring(0, 8) }}...</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="config.platform" label="平台" width="80" />
      
      <el-table-column label="关键词" width="150">
        <template #default="scope">
          <el-tooltip placement="top">
            <template #content>
              <div v-for="keyword in scope.row.config.keywords" :key="keyword">{{ keyword }}</div>
            </template>
            <span class="keywords-preview">
              {{ scope.row.config.keywords.slice(0, 2).join(', ') }}
              <span v-if="scope.row.config.keywords.length > 2">+{{ scope.row.config.keywords.length - 2 }}</span>
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" width="100">
        <template #default="scope">
          <el-tag :type="statusType(scope.row.status)" size="small">
            {{ statusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="进度" width="120">
        <template #default="scope">
          <el-progress 
            :percentage="scope.row.progress" 
            :status="progressStatus(scope.row.status)"
            size="small"
          />
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" width="160">
        <template #default="scope">{{ formatTime(scope.row.created_at) }}</template>
      </el-table-column>
      
      <el-table-column label="开始时间" width="160">
        <template #default="scope">{{ formatTime(scope.row.started_at) }}</template>
      </el-table-column>
      
      <el-table-column label="完成时间" width="160">
        <template #default="scope">{{ formatTime(scope.row.completed_at) }}</template>
      </el-table-column>
      
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <div class="action-buttons">
            <el-button
              size="small"
              type="primary"
              @click="viewTask(scope.row)"
            >
              查看
            </el-button>
            
            <el-button
              v-if="scope.row.status === 'running'"
              size="small"
              type="warning"
              @click="stopTask(scope.row.id)"
            >
              停止
            </el-button>
            
            <el-button
              v-if="scope.row.status === 'completed' && scope.row.result_file"
              size="small"
              type="success"
              @click="downloadResult(scope.row.id)"
            >
              下载
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 空状态 -->
    <el-empty v-if="tasks.length === 0" description="暂无任务历史" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useCrawlerStore } from '@/stores/crawler'
import dayjs from 'dayjs'
import type { CrawlerTask } from '../../../../shared/types/mediacrawler'

const crawlerStore = useCrawlerStore()
const tasks = computed(() => crawlerStore.tasks)

const statusType = (status: string) => {
  const map: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return map[status] || 'info'
}

const statusText = (status: string) => {
  const map: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '已失败'
  }
  return map[status] || status
}

const progressStatus = (status: string) => {
  const map: Record<string, string> = {
    pending: '',
    running: '',
    completed: 'success',
    failed: 'exception'
  }
  return map[status] || ''
}

const formatTime = (time?: Date) => {
  if (!time) return '-'
  return dayjs(time).format('MM-DD HH:mm:ss')
}

const viewTask = (task: CrawlerTask) => {
  crawlerStore.currentTask = task
  if (task.status === 'running') {
    crawlerStore.connectToLogs(task.id)
  }
}

const stopTask = async (taskId: string) => {
  await crawlerStore.stopTask(taskId)
}

const downloadResult = async (taskId: string) => {
  await crawlerStore.downloadResult(taskId)
}
</script>

<style scoped>
.task-history {
  width: 100%;
}

.task-id {
  font-family: monospace;
  font-size: 12px;
  cursor: help;
}

.keywords-preview {
  font-size: 12px;
  color: #606266;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

:deep(.el-table__cell) {
  padding: 8px 0;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>