{"name": "mediacrawler-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.5", "dayjs": "^1.11.10"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.2", "@vue/tsconfig": "^0.5.1", "vite": "^5.0.11", "vue-tsc": "^1.8.25", "typescript": "^5.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "eslint-plugin-vue": "^9.19.2"}}