import express from 'express';
import cors from 'cors';
import { CrawlerController } from './controllers/crawlerController';
import * as path from 'path';

const app = express();
const PORT = process.env.PORT || 3001;
const crawlerController = new CrawlerController();
console.log('进入了111111');

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use('/downloads', express.static(path.join(__dirname, '../../temp/results')));

// API路由
app.post('/api/crawler/start', (req, res) => crawlerController.startCrawler(req, res));
app.get('/api/crawler/tasks', (req, res) => crawlerController.getAllTasks(req, res));
app.get('/api/crawler/tasks/:taskId', (req, res) => crawlerController.getTaskStatus(req, res));
app.post('/api/crawler/tasks/:taskId/stop', (req, res) => crawlerController.stopTask(req, res));
app.get('/api/crawler/tasks/:taskId/download', (req, res) => crawlerController.downloadResult(req, res));
app.get('/api/crawler/tasks/:taskId/logs', (req, res) => crawlerController.streamLogs(req, res));

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 错误处理中间件
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: '服务器内部错误'
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: '接口不存在'
  });
});

app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  console.log(`API文档: http://localhost:${PORT}/api/health`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，准备关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，准备关闭服务器...');
  process.exit(0);
});