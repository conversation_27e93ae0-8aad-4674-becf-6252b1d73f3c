{"name": "mediacrawler-webapp", "version": "1.0.0", "description": "Web interface for MediaCrawler tool", "private": true, "scripts": {"init": "node init-project.js", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build && npm run build:copy", "build:copy": "xcopy ..\\frontend\\dist ..\\backend\\dist\\public /E /I /Y", "start": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "install:all": "npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend"]}