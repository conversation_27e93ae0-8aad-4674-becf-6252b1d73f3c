@echo off
echo 🚀 启动 MediaCrawler Web Interface 开发环境...

REM 检查Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

REM 检查npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm 未安装，请先安装 npm
    pause
    exit /b 1
)

REM 检查MediaCrawler目录
if not exist "..\examples\MediaCrawler" (
    echo ❌ MediaCrawler 目录不存在，请确保 examples\MediaCrawler 已下载
    pause
    exit /b 1
)

echo 📦 安装后端依赖...
cd backend
if not exist "node_modules" (
    npm install
)

echo 📦 安装前端依赖...
cd ..\frontend
if not exist "node_modules" (
    npm install
)

echo 🎯 启动开发服务器...
cd ..

REM 启动后端
echo 🌐 启动后端服务 (端口: 3001)...
cd backend
start npm run dev

REM 启动前端
timeout /t 3 /nobreak >nul
echo 🎨 启动前端服务 (端口: 3000)...
cd ..\frontend
start npm run dev

echo ✅ 启动完成！
echo 前端地址: http://localhost:3000
echo 后端地址: http://localhost:3001
echo 按任意键停止所有服务...
pause