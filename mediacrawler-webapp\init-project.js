#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 初始化 MediaCrawler Web Interface...\n');

// 检查必要的目录和文件
function checkPrerequisites() {
    console.log('📝 检查前置条件...');
    
    // 检查Node.js版本
    try {
        const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
        console.log(`✅ Node.js 版本: ${nodeVersion}`);
    } catch (error) {
        console.error('❌ Node.js 未安装');
        process.exit(1);
    }
    
    // 检查npm
    try {
        const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
        console.log(`✅ npm 版本: ${npmVersion}`);
    } catch (error) {
        console.error('❌ npm 未安装');
        process.exit(1);
    }
    
    // 检查MediaCrawler目录
    const mediaCrawlerPath = path.join(__dirname, '..', 'examples', 'MediaCrawler');
    if (fs.existsSync(mediaCrawlerPath)) {
        console.log(`✅ MediaCrawler 目录已存在: ${mediaCrawlerPath}`);
    } else {
        console.error('❌ MediaCrawler 目录不存在，请确保 examples/MediaCrawler 已下载');
        process.exit(1);
    }
    
    console.log('');
}

// 创建必要的目录
function createDirectories() {
    console.log('📁 创建必要目录...');
    
    const directories = [
        'backend/logs',
        'backend/temp',
        'backend/temp/results',
        'frontend/dist'
    ];
    
    directories.forEach(dir => {
        const fullPath = path.join(__dirname, dir);
        if (!fs.existsSync(fullPath)) {
            fs.mkdirSync(fullPath, { recursive: true });
            console.log(`✅ 创建目录: ${dir}`);
        }
    });
    
    console.log('');
}

// 创建环境配置文件
function createEnvFiles() {
    console.log('⚙️  创建配置文件...');
    
    const envExamplePath = path.join(__dirname, '.env.example');
    const envBackendPath = path.join(__dirname, 'backend', '.env');
    
    if (!fs.existsSync(envBackendPath) && fs.existsSync(envExamplePath)) {
        fs.copyFileSync(envExamplePath, envBackendPath);
        console.log('✅ 创建后端 .env 文件');
    }
    
    console.log('');
}

// 安装依赖
function installDependencies() {
    console.log('📦 安装依赖...');
    
    try {
        // 安装后端依赖
        console.log('🌐 安装后端依赖...');
        execSync('npm install', { 
            cwd: path.join(__dirname, 'backend'),
            stdio: 'inherit'
        });
        
        // 安装前端依赖
        console.log('🎨 安装前端依赖...');
        execSync('npm install', { 
            cwd: path.join(__dirname, 'frontend'),
            stdio: 'inherit'
        });
        
        console.log('✅ 依赖安装完成');
    } catch (error) {
        console.error('❌ 依赖安装失败:', error.message);
        process.exit(1);
    }
    
    console.log('');
}

// 检查Python环境
function checkPythonEnvironment() {
    console.log('🐍 检查Python环境...');
    
    try {
        const pythonVersion = execSync('python --version', { encoding: 'utf8' }).trim();
        console.log(`✅ Python 版本: ${pythonVersion}`);
    } catch (error) {
        try {
            const pythonVersion = execSync('python3 --version', { encoding: 'utf8' }).trim();
            console.log(`✅ Python3 版本: ${pythonVersion}`);
        } catch (error) {
            console.error('❌ Python 未安装');
            process.exit(1);
        }
    }
    
    console.log('');
}

// 主流程
async function main() {
    try {
        checkPrerequisites();
        createDirectories();
        createEnvFiles();
        checkPythonEnvironment();
        installDependencies();
        
        console.log('🎉 项目初始化完成！');
        console.log('');
        console.log('📋 下一步操作:');
        console.log('1. 运行 start-dev.bat (Windows) 或 start-dev.sh (macOS/Linux)');
        console.log('2. 或手动启动:');
        console.log('   - 后端: cd backend && npm run dev');
        console.log('   - 前端: cd frontend && npm run dev');
        console.log('');
        console.log('🌐 访问地址:');
        console.log('   - 前端: http://localhost:3000');
        console.log('   - 后端: http://localhost:3001');
        
    } catch (error) {
        console.error('❌ 初始化失败:', error.message);
        process.exit(1);
    }
}

main();